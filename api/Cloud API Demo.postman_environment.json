{"id": "8296f786-0eeb-41f9-b52f-da3d64ec8718", "name": "Cloud API Demo", "values": [{"key": "ip", "value": "localhost", "type": "default", "enabled": true}, {"key": "port", "value": "6789", "enabled": true}, {"key": "workspace_id", "value": "e3dea0f5-37f2-4d79-ae58-490af3228069", "enabled": true}, {"key": "base_url", "value": "http://{{ip}}:{{port}}", "enabled": true}, {"key": "element_group_id", "value": "e3dea0f5-37f2-4d79-ae58-490af3228060", "enabled": true}, {"key": "media_version", "value": "/media/api/v1", "enabled": true}, {"key": "manage_version", "value": "/manage/api/v1", "enabled": true}, {"key": "map_version", "value": "/map/api/v1", "enabled": true}, {"key": "wayline_version", "value": "/wayline/api/v1", "enabled": true}, {"key": "storage_version", "value": "/storage/api/v1", "enabled": true}, {"key": "control_version", "value": "/control/api/v1", "type": "default", "enabled": true}, {"key": "device_sn", "value": "xxxxxxxxxx", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2022-09-23T03:53:25.999Z", "_postman_exported_using": "Postman/9.31.0"}