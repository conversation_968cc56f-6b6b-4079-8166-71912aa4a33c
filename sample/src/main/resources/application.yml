server:
  port: 6789
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: cloud-api-sample
  datasource:
    druid:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***********************************************************************************************
      username: root
      password: root
      initial-size: 10
      min-idle: 10
      max-active: 20
      max-wait: 60000

  redis:
    host: cloud_api_sample_redis
    port: 6379
    database: 0
    username: # if you enable
    password:
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  servlet:
    multipart:
      max-file-size: 2GB
      max-request-size: 2GB

jwt:
  issuer: DJI
  subject: CloudApiSample
  secret: CloudApiSample
  age: 86400

mqtt:
  # @see com.dji.sample.component.mqtt.model.MqttUseEnum
  # BASIC parameters are required.
  BASIC:
    protocol: MQTT # @see com.dji.sample.component.mqtt.model.MqttProtocolEnum
    host: Please enter your ip.
    port: 1883
    username: JavaServer
    password: 123456
    client-id: 123456
    # If the protocol is ws/wss, this value is required.
    path:
  DRC:
    protocol: WS # @see com.dji.sample.component.mqtt.model.MqttProtocolEnum
    host: Please enter your ip.
    port: 8083
    path: /mqtt
    username: JavaServer
    password: 123456

cloud-sdk:
  mqtt:
    # Topics that need to be subscribed when initially connecting to mqtt, multiple topics are divided by ",".
    inbound-topic: sys/product/+/status,thing/product/+/requests

url:
  manage:
    prefix: manage
    version: /api/v1
  map:
    prefix: map
    version: /api/v1
  media:
    prefix: media
    version: /api/v1
  wayline:
    prefix: wayline
    version: /api/v1
  storage:
    prefix: storage
    version: /api/v1
  control:
    prefix: control
    version: /api/v1

# Tutorial: https://www.alibabacloud.com/help/en/object-storage-service/latest/use-a-temporary-credential-provided-by-sts-to-access-oss
oss:
  enable: false
  provider: ALIYUN # @see com.dji.sample.component.OssConfiguration.model.enums.OssTypeEnum
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  access-key: Please enter your access key.
  secret-key: Please enter your secret key.
  expire: 3600
  region: Please enter your oss region. # cn-hangzhou
  role-session-name: cloudApi
  role-arn: Please enter your role arn. # acs:ram::123456789:role/stsrole
  bucket: Please enter your bucket name.
  object-dir-prefix: Please enter a folder name.

#oss:
#  enable: true
#  provider: aws
#  endpoint: https://s3.us-east-1.amazonaws.com
#  access-key:
#  secret-key:
#  expire: 3600
#  region: us-east-1
#  role-session-name: cloudApi
#  role-arn:
#  bucket: cloudapi-bucket
#  object-dir-prefix: wayline

#oss:
#  enable: true
#  provider: minio
#  endpoint: http://***********:9000
#  access-key: minioadmin
#  secret-key: minioadmin
#  bucket: cloud-bucket
#  expire: 3600
#  region: us-east-1
#  object-dir-prefix: wayline

logging:
  level:
    com.dji: debug
  file:
    name: logs/cloud-api-sample.log

ntp:
  server:
    host: Google.mzr.me

# To create a license for an application: https://developer.dji.com/user/apps/#all
cloud-api:
  app:
    id: Please enter the app id.
    key: Please enter the app key.
    license: Please enter the app license.

livestream:
  url:
    # It is recommended to use a program to create Token. https://github.com/AgoraIO/Tools/blob/master/DynamicKey/AgoraDynamicKey/java/src/main/java/io/agora/media/RtcTokenBuilder2.java
    agora:
      channel: Please enter the agora channel.
      token: Please enter the agora temporary token.
      uid:  654321

    # RTMP  Note: This IP is the address of the streaming server. If you want to see livestream on web page, you need to convert the RTMP stream to WebRTC stream.
    rtmp:
      url: Please enter the rtmp access address.  # Example: 'rtmp://***********/live/'
    rtsp:
      username: Please enter the username.
      password: Please enter the password.
      port: 8554

    # GB28181 Note:If you don't know what these parameters mean, you can go to Pilot2 and select the GB28181 page in the cloud platform. Where the parameters same as these parameters.
    gb28181:
      serverIP: Please enter the server ip.
      serverPort: Please enter the server port.
      serverID: Please enter the server id.
      agentID: Please enter the agent id.
      agentPassword: Please enter the agent password.
      localPort: Please enter the local port.
      channel: Please enter the channel.

    # Webrtc: Only supports using whip standard
    whip:
      url: Please enter the rtmp access address. #  Example：http://***********:1985/rtc/v1/whip/?app=live&stream=